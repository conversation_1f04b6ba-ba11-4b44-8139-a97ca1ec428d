<x-layouts.app title="Become a Volunteer - Vibrant Road Safety Awareness Foundation" description="Join our community of dedicated volunteers and help make roads safer for everyone. Discover volunteer opportunities and how you can make a difference.">
    <!-- Hero Section -->
    <x-hero 
        title="Make a <span class='text-safety-yellow'>Difference</span>"
        subtitle="Become a Volunteer"
        description="Join our community of passionate volunteers who are dedicated to making roads safer for everyone. Your time and skills can help save lives and strengthen communities."
        size="lg"
    />

    <!-- Why Volunteer Section -->
    <x-section padding="lg">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Why Volunteer With Us?</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Volunteering with VRSAF offers meaningful opportunities to contribute to road safety while developing new skills and connecting with like-minded individuals.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @php
            $benefits = [
                [
                    'title' => 'Make a Real Impact',
                    'description' => 'Your efforts directly contribute to saving lives and preventing accidents in your community.',
                    'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/></svg>',
                    'color' => 'safety-red'
                ],
                [
                    'title' => 'Develop New Skills',
                    'description' => 'Gain valuable experience in education, public speaking, event management, and community outreach.',
                    'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/></svg>',
                    'color' => 'safety-yellow'
                ],
                [
                    'title' => 'Build Community',
                    'description' => 'Connect with passionate individuals who share your commitment to road safety and community service.',
                    'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/></svg>',
                    'color' => 'safety-green'
                ],
                [
                    'title' => 'Flexible Commitment',
                    'description' => 'Choose volunteer opportunities that fit your schedule and availability, from one-time events to ongoing programs.',
                    'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/></svg>',
                    'color' => 'primary'
                ],
                [
                    'title' => 'Professional Growth',
                    'description' => 'Enhance your resume with meaningful volunteer experience and develop leadership capabilities.',
                    'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/></svg>',
                    'color' => 'safety-orange'
                ],
                [
                    'title' => 'Recognition & Rewards',
                    'description' => 'Receive certificates, awards, and recognition for your contributions to road safety advocacy.',
                    'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/></svg>',
                    'color' => 'secondary'
                ]
            ];
            @endphp
            
            @foreach($benefits as $benefit)
                <div class="text-center group">
                    <div class="w-16 h-16 bg-{{ $benefit['color'] }}/10 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-{{ $benefit['color'] }}/20 transition-smooth">
                        <div class="w-8 h-8 text-{{ $benefit['color'] }}">
                            {!! $benefit['icon'] !!}
                        </div>
                    </div>
                    <h3 class="text-lg font-semibold text-foreground mb-2">{{ $benefit['title'] }}</h3>
                    <p class="text-sm text-muted-foreground leading-relaxed">{{ $benefit['description'] }}</p>
                </div>
            @endforeach
        </div>
    </x-section>

    <!-- Volunteer Opportunities -->
    <x-section background="muted" padding="lg">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Volunteer Opportunities</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Find the perfect volunteer role that matches your interests, skills, and availability.
            </p>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            @php
            $opportunities = [
                [
                    'title' => 'Community Safety Educator',
                    'commitment' => '4-6 hours/month',
                    'location' => 'Various community locations',
                    'description' => 'Deliver safety presentations to schools, community groups, and organizations.',
                    'requirements' => [
                        'Comfortable with public speaking',
                        'Reliable transportation',
                        'Complete training program',
                        'Background check required'
                    ],
                    'benefits' => [
                        'Comprehensive training provided',
                        'Flexible scheduling',
                        'Presentation materials supplied',
                        'Ongoing support and mentorship'
                    ]
                ],
                [
                    'title' => 'Event Support Volunteer',
                    'commitment' => '2-4 hours per event',
                    'location' => 'Event venues across the region',
                    'description' => 'Assist with setup, registration, and coordination at safety events and workshops.',
                    'requirements' => [
                        'Friendly and outgoing personality',
                        'Ability to stand for extended periods',
                        'Team player attitude',
                        'Punctual and reliable'
                    ],
                    'benefits' => [
                        'Meet diverse community members',
                        'Learn about event management',
                        'Flexible event selection',
                        'Free event materials and refreshments'
                    ]
                ],
                [
                    'title' => 'Digital Content Creator',
                    'commitment' => '3-5 hours/week',
                    'location' => 'Remote/Home-based',
                    'description' => 'Create social media content, blog posts, and digital materials for safety campaigns.',
                    'requirements' => [
                        'Strong writing or design skills',
                        'Social media experience',
                        'Creative mindset',
                        'Basic computer skills'
                    ],
                    'benefits' => [
                        'Work from home flexibility',
                        'Build digital portfolio',
                        'Creative freedom',
                        'Professional development opportunities'
                    ]
                ],
                [
                    'title' => 'Administrative Support',
                    'commitment' => '2-3 hours/week',
                    'location' => 'Foundation office or remote',
                    'description' => 'Provide administrative support including data entry, filing, and general office tasks.',
                    'requirements' => [
                        'Basic computer skills',
                        'Attention to detail',
                        'Organizational skills',
                        'Professional communication'
                    ],
                    'benefits' => [
                        'Office experience',
                        'Flexible hours',
                        'Professional references',
                        'Skill development opportunities'
                    ]
                ]
            ];
            @endphp
            
            @foreach($opportunities as $opportunity)
                <x-card variant="elevated" hover="true" class="group">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-bold text-foreground group-hover:text-primary transition-smooth">
                            {{ $opportunity['title'] }}
                        </h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
                            {{ $opportunity['commitment'] }}
                        </span>
                    </div>
                    
                    <div class="flex items-center text-sm text-muted-foreground mb-4">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                        </svg>
                        {{ $opportunity['location'] }}
                    </div>
                    
                    <p class="text-muted-foreground mb-6 leading-relaxed">
                        {{ $opportunity['description'] }}
                    </p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-foreground mb-3">Requirements:</h4>
                            <ul class="space-y-2">
                                @foreach($opportunity['requirements'] as $requirement)
                                    <li class="flex items-start space-x-2">
                                        <svg class="w-4 h-4 text-primary mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"/>
                                        </svg>
                                        <span class="text-sm text-muted-foreground">{{ $requirement }}</span>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-foreground mb-3">Benefits:</h4>
                            <ul class="space-y-2">
                                @foreach($opportunity['benefits'] as $benefit)
                                    <li class="flex items-start space-x-2">
                                        <svg class="w-4 h-4 text-success mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        <span class="text-sm text-muted-foreground">{{ $benefit }}</span>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-6 pt-6 border-t border-border">
                        <x-button href="#volunteer-form" size="sm" class="w-full">
                            Apply for This Role
                        </x-button>
                    </div>
                </x-card>
            @endforeach
        </div>
    </x-section>

    <!-- Volunteer Application Form -->
    <x-section padding="lg" id="volunteer-form">
        <div class="max-w-3xl mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Join Our Team</h2>
                <p class="text-lg text-muted-foreground">
                    Ready to make a difference? Fill out the form below to start your volunteer journey with us.
                </p>
            </div>
            
            <x-card variant="elevated">
                <form class="space-y-6" id="volunteer-application-form">
                    <!-- Personal Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-foreground mb-2">First Name *</label>
                            <input type="text" id="first_name" name="first_name" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                        </div>
                        <div>
                            <label for="last_name" class="block text-sm font-medium text-foreground mb-2">Last Name *</label>
                            <input type="text" id="last_name" name="last_name" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="email" class="block text-sm font-medium text-foreground mb-2">Email Address *</label>
                            <input type="email" id="email" name="email" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                        </div>
                        <div>
                            <label for="phone" class="block text-sm font-medium text-foreground mb-2">Phone Number *</label>
                            <input type="tel" id="phone" name="phone" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                        </div>
                    </div>
                    
                    <div>
                        <label for="address" class="block text-sm font-medium text-foreground mb-2">Address</label>
                        <textarea id="address" name="address" rows="2" class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"></textarea>
                    </div>
                    
                    <!-- Volunteer Preferences -->
                    <div>
                        <label class="block text-sm font-medium text-foreground mb-3">Volunteer Opportunities of Interest *</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <label class="flex items-center">
                                <input type="checkbox" name="opportunities[]" value="educator" class="rounded border-input text-primary focus:ring-ring">
                                <span class="ml-2 text-sm text-foreground">Community Safety Educator</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="opportunities[]" value="event_support" class="rounded border-input text-primary focus:ring-ring">
                                <span class="ml-2 text-sm text-foreground">Event Support Volunteer</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="opportunities[]" value="content_creator" class="rounded border-input text-primary focus:ring-ring">
                                <span class="ml-2 text-sm text-foreground">Digital Content Creator</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="opportunities[]" value="admin_support" class="rounded border-input text-primary focus:ring-ring">
                                <span class="ml-2 text-sm text-foreground">Administrative Support</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="availability" class="block text-sm font-medium text-foreground mb-2">Availability *</label>
                            <select id="availability" name="availability" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                                <option value="">Select availability</option>
                                <option value="weekdays">Weekdays</option>
                                <option value="weekends">Weekends</option>
                                <option value="both">Both weekdays and weekends</option>
                                <option value="flexible">Flexible</option>
                            </select>
                        </div>
                        <div>
                            <label for="commitment" class="block text-sm font-medium text-foreground mb-2">Time Commitment *</label>
                            <select id="commitment" name="commitment" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                                <option value="">Select commitment level</option>
                                <option value="1-2_hours">1-2 hours per week</option>
                                <option value="3-5_hours">3-5 hours per week</option>
                                <option value="6-10_hours">6-10 hours per week</option>
                                <option value="events_only">Events only</option>
                            </select>
                        </div>
                    </div>
                    
                    <div>
                        <label for="experience" class="block text-sm font-medium text-foreground mb-2">Relevant Experience or Skills</label>
                        <textarea id="experience" name="experience" rows="4" placeholder="Tell us about any relevant experience, skills, or qualifications you have..." class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"></textarea>
                    </div>
                    
                    <div>
                        <label for="motivation" class="block text-sm font-medium text-foreground mb-2">Why do you want to volunteer with us? *</label>
                        <textarea id="motivation" name="motivation" rows="4" required placeholder="Share your motivation for volunteering with the Vibrant Road Safety Awareness Foundation..." class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"></textarea>
                    </div>
                    
                    <!-- Agreement -->
                    <div class="space-y-3">
                        <label class="flex items-start">
                            <input type="checkbox" name="background_check" required class="mt-1 rounded border-input text-primary focus:ring-ring">
                            <span class="ml-2 text-sm text-muted-foreground">I understand that a background check may be required for certain volunteer positions *</span>
                        </label>
                        <label class="flex items-start">
                            <input type="checkbox" name="terms" required class="mt-1 rounded border-input text-primary focus:ring-ring">
                            <span class="ml-2 text-sm text-muted-foreground">I agree to the volunteer terms and conditions and privacy policy *</span>
                        </label>
                        <label class="flex items-start">
                            <input type="checkbox" name="newsletter" class="mt-1 rounded border-input text-primary focus:ring-ring">
                            <span class="ml-2 text-sm text-muted-foreground">I would like to receive updates about volunteer opportunities and foundation news</span>
                        </label>
                    </div>
                    
                    <div class="pt-6">
                        <x-button type="submit" size="lg" class="w-full">
                            Submit Application
                            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                            </svg>
                        </x-button>
                    </div>
                </form>
            </x-card>
        </div>
    </x-section>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('volunteer-application-form');
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = 'Submitting...';
                submitBtn.disabled = true;
                
                // Simulate form submission
                setTimeout(() => {
                    alert('Thank you for your application! We will review it and contact you within 3-5 business days.');
                    form.reset();
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 2000);
            });
        });
    </script>
    @endpush
</x-layouts.app>
