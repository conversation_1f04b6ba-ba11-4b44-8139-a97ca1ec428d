@props([
    'variant' => 'default', // default, elevated, bordered, ghost
    'padding' => 'default', // none, sm, default, lg, xl
    'hover' => false,
    'clickable' => false,
    'href' => null
])

@php
$baseClasses = 'bg-card text-card-foreground rounded-lg transition-smooth';

$variantClasses = match($variant) {
    'elevated' => 'shadow-lg border border-border',
    'bordered' => 'border border-border',
    'ghost' => 'bg-transparent',
    default => 'shadow-sm border border-border'
};

$paddingClasses = match($padding) {
    'none' => '',
    'sm' => 'p-4',
    'lg' => 'p-8',
    'xl' => 'p-12',
    default => 'p-6'
};

$hoverClasses = $hover ? 'hover:shadow-md hover:-translate-y-1' : '';
$clickableClasses = $clickable ? 'cursor-pointer' : '';

$classes = trim("{$baseClasses} {$variantClasses} {$paddingClasses} {$hoverClasses} {$clickableClasses}");
@endphp

@if($href)
    <a href="{{ $href }}" {{ $attributes->merge(['class' => $classes]) }}>
        {{ $slot }}
    </a>
@else
    <div {{ $attributes->merge(['class' => $classes]) }}>
        {{ $slot }}
    </div>
@endif
