@props([
    'title' => '',
    'subtitle' => '',
    'description' => '',
    'background' => 'gradient', // gradient, image, solid
    'backgroundImage' => null,
    'overlay' => true,
    'centered' => true,
    'size' => 'default', // sm, default, lg, xl
    'actions' => null
])

@php
$sizeClasses = match($size) {
    'sm' => 'py-16',
    'lg' => 'py-32',
    'xl' => 'py-40',
    default => 'py-24'
};

$backgroundClasses = match($background) {
    'image' => $backgroundImage ? "bg-cover bg-center bg-no-repeat" : 'bg-gradient-to-br from-primary via-primary-hover to-primary',
    'solid' => 'bg-primary',
    default => 'bg-gradient-to-br from-primary via-primary-hover to-primary'
};

$textAlignment = $centered ? 'text-center' : 'text-left';
$contentAlignment = $centered ? 'items-center' : 'items-start';
@endphp

<section 
    class="relative {{ $sizeClasses }} {{ $backgroundClasses }} text-primary-foreground overflow-hidden"
    @if($background === 'image' && $backgroundImage)
        style="background-image: url('{{ $backgroundImage }}')"
    @endif
>
    @if($overlay && ($background === 'image' || $background === 'gradient'))
        <div class="absolute inset-0 bg-black/40"></div>
    @endif
    
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="hero-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                    <circle cx="10" cy="10" r="1" fill="currentColor"/>
                </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#hero-pattern)"/>
        </svg>
    </div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col {{ $contentAlignment }} {{ $textAlignment }} space-y-6">
            @if($subtitle)
                <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-foreground/20 text-primary-foreground border border-primary-foreground/30">
                    {{ $subtitle }}
                </div>
            @endif
            
            @if($title)
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                    {!! $title !!}
                </h1>
            @endif
            
            @if($description)
                <p class="text-lg md:text-xl text-primary-foreground/90 max-w-3xl leading-relaxed">
                    {{ $description }}
                </p>
            @endif
            
            @if($actions)
                <div class="flex flex-col sm:flex-row gap-4 {{ $centered ? 'justify-center' : 'justify-start' }}">
                    {!! $actions !!}
                </div>
            @endif
            
            {{ $slot }}
        </div>
    </div>
    
    <!-- Decorative Elements -->
    <div class="absolute bottom-0 left-0 right-0">
        <svg class="w-full h-12 text-background" viewBox="0 0 1200 120" preserveAspectRatio="none">
            <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="currentColor"></path>
            <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="currentColor"></path>
            <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="currentColor"></path>
        </svg>
    </div>
</section>
