<x-layouts.app title="Media Coverage - Vibrant Road Safety Awareness Foundation" description="Latest media coverage, news articles, and press releases about our road safety initiatives and community impact.">
    <!-- Hero Section -->
    <x-hero 
        title="In the <span class='text-safety-yellow'>Media</span>"
        subtitle="Media Coverage"
        description="Stay updated with our latest news, media appearances, and press coverage as we work to promote road safety awareness across communities."
        size="lg"
    />

    <!-- Featured Coverage -->
    <x-section padding="lg">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Featured Coverage</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Recent highlights from major media outlets covering our road safety initiatives and community impact.
            </p>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            @php
            $featuredStories = [
                [
                    'title' => 'Local Foundation\'s Road Safety Program Reduces Accidents by 40%',
                    'outlet' => 'City News Network',
                    'date' => 'March 15, 2024',
                    'type' => 'News Article',
                    'excerpt' => 'A comprehensive study reveals that communities participating in the Vibrant Road Safety Awareness Foundation\'s education programs have experienced a significant 40% reduction in traffic accidents over the past two years.',
                    'image' => 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=600&h=400&fit=crop',
                    'readTime' => '5 min read',
                    'featured' => true
                ],
                [
                    'title' => 'Innovative Training Program Transforms Commercial Driver Safety',
                    'outlet' => 'Transport Weekly',
                    'date' => 'March 12, 2024',
                    'type' => 'Feature Story',
                    'excerpt' => 'The foundation\'s Professional Driver Excellence Program has certified over 2,000 commercial drivers, leading to a 60% reduction in commercial vehicle incidents and setting new industry standards.',
                    'image' => 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=600&h=400&fit=crop',
                    'readTime' => '7 min read',
                    'featured' => true
                ]
            ];
            @endphp
            
            @foreach($featuredStories as $story)
                <x-card variant="elevated" hover="true" class="group overflow-hidden">
                    <div class="aspect-video bg-gradient-to-br from-primary/20 to-primary/5 rounded-lg overflow-hidden mb-6">
                        <img src="{{ $story['image'] }}" alt="{{ $story['title'] }}" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                    </div>
                    
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-3">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
                                {{ $story['type'] }}
                            </span>
                            <span class="text-sm text-muted-foreground">{{ $story['readTime'] }}</span>
                        </div>
                        <span class="text-sm text-muted-foreground">{{ $story['date'] }}</span>
                    </div>
                    
                    <h3 class="text-xl font-bold text-foreground mb-3 group-hover:text-primary transition-smooth line-clamp-2">
                        {{ $story['title'] }}
                    </h3>
                    
                    <p class="text-muted-foreground mb-4 leading-relaxed line-clamp-3">
                        {{ $story['excerpt'] }}
                    </p>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-secondary/20 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-secondary-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"/>
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-foreground">{{ $story['outlet'] }}</span>
                        </div>
                        <x-button href="#" size="sm" variant="ghost">
                            Read More
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </x-button>
                    </div>
                </x-card>
            @endforeach
        </div>
    </x-section>

    <!-- Recent Coverage -->
    <x-section background="muted" padding="lg">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Recent Coverage</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Latest news articles, interviews, and media mentions from various outlets.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @php
            $recentCoverage = [
                [
                    'title' => 'School Safety Program Reaches 25,000 Students',
                    'outlet' => 'Education Today',
                    'date' => 'March 10, 2024',
                    'type' => 'News Article',
                    'excerpt' => 'The foundation\'s Safe Schools Initiative has successfully educated over 25,000 students across 150 schools.',
                    'logo' => 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=100&h=100&fit=crop'
                ],
                [
                    'title' => 'Director Discusses Road Safety on Morning Show',
                    'outlet' => 'Morning Radio',
                    'date' => 'March 8, 2024',
                    'type' => 'Radio Interview',
                    'excerpt' => 'Executive Director Dr. Sarah Johnson shares insights on community-based road safety education.',
                    'logo' => 'https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=100&h=100&fit=crop'
                ],
                [
                    'title' => 'Community Champions Program Expands Statewide',
                    'outlet' => 'State Herald',
                    'date' => 'March 5, 2024',
                    'type' => 'Press Release',
                    'excerpt' => 'The successful Community Safety Champions program is expanding to reach all 50 counties.',
                    'logo' => 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=100&h=100&fit=crop'
                ],
                [
                    'title' => 'Technology Integration in Safety Education',
                    'outlet' => 'Tech Innovation Weekly',
                    'date' => 'March 3, 2024',
                    'type' => 'Feature Article',
                    'excerpt' => 'How the foundation is using VR and mobile apps to enhance road safety education.',
                    'logo' => 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=100&h=100&fit=crop'
                ],
                [
                    'title' => 'Partnership with State Transportation Department',
                    'outlet' => 'Government News',
                    'date' => 'February 28, 2024',
                    'type' => 'Press Release',
                    'excerpt' => 'New partnership aims to integrate safety education into state transportation planning.',
                    'logo' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop'
                ],
                [
                    'title' => 'Volunteer Program Reaches 1,500 Active Members',
                    'outlet' => 'Community Voice',
                    'date' => 'February 25, 2024',
                    'type' => 'Community News',
                    'excerpt' => 'The foundation\'s volunteer program continues to grow with dedicated community members.',
                    'logo' => 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=100&h=100&fit=crop'
                ]
            ];
            @endphp
            
            @foreach($recentCoverage as $coverage)
                <x-card hover="true" class="group">
                    <div class="flex items-start space-x-4 mb-4">
                        <div class="w-12 h-12 rounded-lg overflow-hidden bg-gradient-to-br from-secondary/20 to-secondary/5 flex-shrink-0">
                            <img src="{{ $coverage['logo'] }}" alt="{{ $coverage['outlet'] }}" class="w-full h-full object-cover">
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-secondary/10 text-secondary-foreground">
                                    {{ $coverage['type'] }}
                                </span>
                            </div>
                            <h4 class="font-medium text-foreground text-sm">{{ $coverage['outlet'] }}</h4>
                            <p class="text-xs text-muted-foreground">{{ $coverage['date'] }}</p>
                        </div>
                    </div>
                    
                    <h3 class="text-lg font-semibold text-foreground mb-2 group-hover:text-primary transition-smooth line-clamp-2">
                        {{ $coverage['title'] }}
                    </h3>
                    
                    <p class="text-sm text-muted-foreground mb-4 leading-relaxed line-clamp-3">
                        {{ $coverage['excerpt'] }}
                    </p>
                    
                    <div class="flex items-center justify-between">
                        <x-button href="#" size="sm" variant="ghost">
                            Read Article
                        </x-button>
                        <button class="text-muted-foreground hover:text-foreground transition-smooth">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                            </svg>
                        </button>
                    </div>
                </x-card>
            @endforeach
        </div>
    </x-section>

    <!-- Press Releases -->
    <x-section padding="lg">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Press Releases</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Official announcements and press releases from the Vibrant Road Safety Awareness Foundation.
            </p>
        </div>
        
        <div class="max-w-4xl mx-auto space-y-6">
            @php
            $pressReleases = [
                [
                    'title' => 'VRSAF Announces $2.5M Grant for Highway Safety Corridor Project',
                    'date' => 'March 18, 2024',
                    'excerpt' => 'The foundation has received a major grant to implement comprehensive safety improvements along high-risk highway segments, expected to save hundreds of lives over the next five years.',
                    'category' => 'Funding'
                ],
                [
                    'title' => 'New Partnership with National Transportation Safety Board',
                    'date' => 'March 14, 2024',
                    'excerpt' => 'VRSAF partners with NTSB to develop advanced safety training programs and share best practices for accident prevention nationwide.',
                    'category' => 'Partnership'
                ],
                [
                    'title' => 'Foundation Launches Mobile Safety Education Unit',
                    'date' => 'March 11, 2024',
                    'excerpt' => 'A state-of-the-art mobile unit will bring interactive safety education directly to underserved communities across the region.',
                    'category' => 'Program Launch'
                ],
                [
                    'title' => 'Annual Road Safety Conference Set for May 2024',
                    'date' => 'March 7, 2024',
                    'excerpt' => 'The foundation announces its annual conference bringing together safety experts, policymakers, and community leaders from across the nation.',
                    'category' => 'Event'
                ]
            ];
            @endphp
            
            @foreach($pressReleases as $release)
                <div class="flex items-start space-x-6 p-6 bg-card border border-border rounded-lg hover:shadow-md transition-smooth group">
                    <div class="flex-shrink-0">
                        <div class="w-16 h-16 bg-primary/10 rounded-lg flex items-center justify-center">
                            <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-3 mb-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-accent/10 text-accent-foreground">
                                {{ $release['category'] }}
                            </span>
                            <span class="text-sm text-muted-foreground">{{ $release['date'] }}</span>
                        </div>
                        <h3 class="text-xl font-semibold text-foreground mb-2 group-hover:text-primary transition-smooth">
                            {{ $release['title'] }}
                        </h3>
                        <p class="text-muted-foreground leading-relaxed mb-4">
                            {{ $release['excerpt'] }}
                        </p>
                        <div class="flex items-center space-x-4">
                            <x-button href="#" size="sm">
                                Read Full Release
                            </x-button>
                            <button class="text-sm text-muted-foreground hover:text-foreground transition-smooth flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                                <span>Download PDF</span>
                            </button>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </x-section>

    <!-- Media Contact -->
    <x-section background="primary" padding="lg">
        <div class="text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-primary-foreground mb-4">
                Media Inquiries
            </h2>
            <p class="text-lg text-primary-foreground/90 max-w-2xl mx-auto mb-8">
                For press inquiries, interview requests, or additional information about our programs and initiatives, please contact our media relations team.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <x-button href="mailto:<EMAIL>" variant="secondary" size="lg">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                    <EMAIL>
                </x-button>
                <x-button href="tel:+15551234567" variant="outline" size="lg" class="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                    </svg>
                    (*************
                </x-button>
            </div>
        </div>
    </x-section>
</x-layouts.app>
