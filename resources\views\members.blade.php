<x-layouts.app title="Our Members - Vibrant Road Safety Awareness Foundation" description="Meet our dedicated team of professionals, volunteers, and board members who are committed to making roads safer for everyone.">
    <!-- Hero Section -->
    <x-hero 
        title="Meet Our <span class='text-safety-yellow'>Dedicated Team</span>"
        subtitle="Our Members"
        description="Our diverse team of professionals, volunteers, and advocates brings together expertise from various fields to advance road safety education and awareness."
        size="lg"
    />

    <!-- Leadership Team -->
    <x-section padding="lg">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Leadership Team</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Our experienced leadership team guides the strategic direction and ensures the effectiveness of our programs.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @php
            $leaders = [
                [
                    'name' => 'Dr. <PERSON>',
                    'position' => 'Executive Director',
                    'bio' => 'With over 15 years in public health and road safety research, Dr<PERSON> leads our strategic initiatives and program development.',
                    'expertise' => ['Public Health', 'Policy Development', 'Research'],
                    'image' => 'https://images.unsplash.com/photo-1494790108755-2616b9e0e4d4?w=400&h=400&fit=crop&crop=face'
                ],
                [
                    'name' => 'Michael Chen',
                    'position' => 'Program Director',
                    'bio' => 'A former traffic engineer with extensive experience in road safety systems and community education program management.',
                    'expertise' => ['Traffic Engineering', 'Program Management', 'Community Outreach'],
                    'image' => 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face'
                ],
                [
                    'name' => 'Lisa Rodriguez',
                    'position' => 'Training Coordinator',
                    'bio' => 'Specializes in adult education and professional development with a focus on driver training and capacity building programs.',
                    'expertise' => ['Adult Education', 'Training Development', 'Curriculum Design'],
                    'image' => 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face'
                ],
                [
                    'name' => 'David Thompson',
                    'position' => 'Community Engagement Manager',
                    'bio' => 'Builds partnerships with local organizations and manages volunteer programs to expand our community reach.',
                    'expertise' => ['Community Relations', 'Volunteer Management', 'Partnership Development'],
                    'image' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face'
                ],
                [
                    'name' => 'Dr. Priya Patel',
                    'position' => 'Research Director',
                    'bio' => 'Leads our research initiatives and data analysis to measure program effectiveness and inform policy recommendations.',
                    'expertise' => ['Data Analysis', 'Research Methodology', 'Policy Research'],
                    'image' => 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=400&h=400&fit=crop&crop=face'
                ],
                [
                    'name' => 'James Wilson',
                    'position' => 'Operations Manager',
                    'bio' => 'Oversees daily operations, logistics coordination, and ensures smooth execution of all foundation activities.',
                    'expertise' => ['Operations Management', 'Logistics', 'Project Coordination'],
                    'image' => 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=400&fit=crop&crop=face'
                ]
            ];
            @endphp
            
            @foreach($leaders as $leader)
                <x-card hover="true" class="text-center group">
                    <div class="relative mb-6">
                        <div class="w-32 h-32 mx-auto rounded-full overflow-hidden bg-gradient-to-br from-primary/20 to-primary/5">
                            <img src="{{ $leader['image'] }}" alt="{{ $leader['name'] }}" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                        </div>
                        <div class="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                            <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-primary-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-xl font-semibold text-foreground mb-1">{{ $leader['name'] }}</h3>
                    <p class="text-primary font-medium mb-3">{{ $leader['position'] }}</p>
                    <p class="text-sm text-muted-foreground mb-4 leading-relaxed">{{ $leader['bio'] }}</p>
                    <div class="flex flex-wrap gap-2 justify-center">
                        @foreach($leader['expertise'] as $skill)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
                                {{ $skill }}
                            </span>
                        @endforeach
                    </div>
                </x-card>
            @endforeach
        </div>
    </x-section>

    <!-- Board of Directors -->
    <x-section background="muted" padding="lg">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Board of Directors</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Our board provides strategic oversight and governance to ensure we fulfill our mission effectively.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            @php
            $boardMembers = [
                [
                    'name' => 'Hon. Robert Martinez',
                    'position' => 'Board Chairman',
                    'background' => 'Former Transportation Secretary',
                    'image' => 'https://images.unsplash.com/photo-**********-0b93528c311a?w=300&h=300&fit=crop&crop=face'
                ],
                [
                    'name' => 'Dr. Angela Foster',
                    'position' => 'Vice Chairman',
                    'background' => 'Public Health Expert',
                    'image' => 'https://images.unsplash.com/photo-**********-deb4988cc6c0?w=300&h=300&fit=crop&crop=face'
                ],
                [
                    'name' => 'Mark Stevens',
                    'position' => 'Treasurer',
                    'background' => 'Financial Management Specialist',
                    'image' => 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=300&h=300&fit=crop&crop=face'
                ],
                [
                    'name' => 'Jennifer Kim',
                    'position' => 'Secretary',
                    'background' => 'Legal Affairs Consultant',
                    'image' => 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=300&h=300&fit=crop&crop=face'
                ]
            ];
            @endphp
            
            @foreach($boardMembers as $member)
                <div class="text-center group">
                    <div class="w-24 h-24 mx-auto rounded-full overflow-hidden bg-gradient-to-br from-secondary/20 to-secondary/5 mb-4">
                        <img src="{{ $member['image'] }}" alt="{{ $member['name'] }}" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                    </div>
                    <h3 class="text-lg font-semibold text-foreground mb-1">{{ $member['name'] }}</h3>
                    <p class="text-primary font-medium text-sm mb-1">{{ $member['position'] }}</p>
                    <p class="text-xs text-muted-foreground">{{ $member['background'] }}</p>
                </div>
            @endforeach
        </div>
    </x-section>

    <!-- Advisory Committee -->
    <x-section padding="lg">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Advisory Committee</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Distinguished experts who provide guidance and expertise in specialized areas of road safety.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            @php
            $advisors = [
                [
                    'name' => 'Prof. William Anderson',
                    'title' => 'Traffic Safety Research',
                    'affiliation' => 'University Transportation Center',
                    'contribution' => 'Provides research insights and academic perspective on traffic safety innovations.'
                ],
                [
                    'name' => 'Chief Maria Santos',
                    'title' => 'Law Enforcement Liaison',
                    'affiliation' => 'State Highway Patrol',
                    'contribution' => 'Bridges the gap between enforcement and education for comprehensive safety approaches.'
                ],
                [
                    'name' => 'Dr. Ahmed Hassan',
                    'title' => 'Emergency Medicine Advisor',
                    'affiliation' => 'Regional Medical Center',
                    'contribution' => 'Offers medical perspective on accident prevention and trauma reduction strategies.'
                ],
                [
                    'name' => 'Susan Taylor',
                    'title' => 'Community Advocacy',
                    'affiliation' => 'Citizens for Safe Streets',
                    'contribution' => 'Represents community voice and grassroots advocacy in our program development.'
                ]
            ];
            @endphp
            
            @foreach($advisors as $advisor)
                <div class="flex items-start space-x-4 p-6 bg-card rounded-lg border border-border hover:shadow-md transition-smooth">
                    <div class="w-16 h-16 bg-gradient-to-br from-accent/20 to-accent/5 rounded-full flex items-center justify-center flex-shrink-0">
                        <svg class="w-8 h-8 text-accent-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-foreground mb-1">{{ $advisor['name'] }}</h3>
                        <p class="text-primary font-medium text-sm mb-1">{{ $advisor['title'] }}</p>
                        <p class="text-xs text-muted-foreground mb-3">{{ $advisor['affiliation'] }}</p>
                        <p class="text-sm text-muted-foreground leading-relaxed">{{ $advisor['contribution'] }}</p>
                    </div>
                </div>
            @endforeach
        </div>
    </x-section>

    <!-- Join Our Team -->
    <x-section background="primary" padding="lg">
        <div class="text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-primary-foreground mb-4">
                Join Our Team
            </h2>
            <p class="text-lg text-primary-foreground/90 max-w-2xl mx-auto mb-8">
                We're always looking for passionate individuals who share our commitment to road safety. 
                Explore opportunities to contribute your skills and expertise to our mission.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <x-button href="{{ route('volunteer.index') }}" variant="secondary" size="lg">
                    Volunteer Opportunities
                </x-button>
                <x-button href="#" variant="outline" size="lg" class="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
                    Career Opportunities
                </x-button>
            </div>
        </div>
    </x-section>
</x-layouts.app>
