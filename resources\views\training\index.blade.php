<x-layouts.app title="Capacity Building & Training - Vibrant Road Safety Awareness Foundation" description="Professional training programs and capacity building initiatives to enhance road safety knowledge and skills across communities.">
    <!-- Hero Section -->
    <x-hero 
        title="Building <span class='text-safety-yellow'>Safety Capacity</span>"
        subtitle="Training & Development"
        description="Empowering individuals and organizations with the knowledge, skills, and tools needed to create safer road environments through comprehensive training programs."
        size="lg"
    />

    <!-- Training Programs Overview -->
    <x-section padding="lg">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Our Training Programs</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Comprehensive, evidence-based training designed for different audiences and skill levels.
            </p>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            @php
            $programs = [
                [
                    'title' => 'Professional Driver Certification',
                    'target' => 'Commercial Drivers',
                    'duration' => '40 hours',
                    'format' => 'In-person & Online',
                    'description' => 'Comprehensive certification program covering defensive driving, vehicle maintenance, emergency procedures, and regulatory compliance.',
                    'modules' => [
                        'Defensive Driving Techniques',
                        'Vehicle Safety Inspection',
                        'Emergency Response Protocols',
                        'Regulatory Compliance',
                        'Fatigue Management',
                        'Customer Safety Protocols'
                    ],
                    'benefits' => [
                        'Industry-recognized certification',
                        'Reduced insurance premiums',
                        'Enhanced employment opportunities',
                        'Improved safety record'
                    ],
                    'color' => 'primary'
                ],
                [
                    'title' => 'Community Safety Leadership',
                    'target' => 'Community Leaders',
                    'duration' => '24 hours',
                    'format' => 'Workshop Series',
                    'description' => 'Leadership development program for community members who want to champion road safety initiatives in their neighborhoods.',
                    'modules' => [
                        'Community Assessment Techniques',
                        'Safety Program Development',
                        'Stakeholder Engagement',
                        'Event Planning & Management',
                        'Public Speaking & Advocacy',
                        'Data Collection & Analysis'
                    ],
                    'benefits' => [
                        'Leadership certification',
                        'Network of safety advocates',
                        'Resource toolkit',
                        'Ongoing mentorship support'
                    ],
                    'color' => 'safety-green'
                ],
                [
                    'title' => 'Educator Training Program',
                    'target' => 'Teachers & Instructors',
                    'duration' => '16 hours',
                    'format' => 'Interactive Workshops',
                    'description' => 'Specialized training for educators to integrate road safety education into their curriculum and teaching practices.',
                    'modules' => [
                        'Age-Appropriate Safety Concepts',
                        'Interactive Teaching Methods',
                        'Curriculum Integration',
                        'Assessment Strategies',
                        'Parent Engagement',
                        'Resource Development'
                    ],
                    'benefits' => [
                        'Teaching certification',
                        'Curriculum resources',
                        'Student engagement tools',
                        'Professional development credits'
                    ],
                    'color' => 'safety-orange'
                ],
                [
                    'title' => 'Workplace Safety Coordinator',
                    'target' => 'HR & Safety Officers',
                    'duration' => '32 hours',
                    'format' => 'Blended Learning',
                    'description' => 'Training program for workplace safety coordinators to implement comprehensive road safety programs in their organizations.',
                    'modules' => [
                        'Risk Assessment & Management',
                        'Policy Development',
                        'Training Program Design',
                        'Incident Investigation',
                        'Safety Culture Development',
                        'Performance Monitoring'
                    ],
                    'benefits' => [
                        'Professional certification',
                        'Implementation toolkit',
                        'Legal compliance guidance',
                        'Peer network access'
                    ],
                    'color' => 'safety-yellow'
                ]
            ];
            @endphp
            
            @foreach($programs as $program)
                <x-card variant="elevated" hover="true" class="group">
                    <div class="flex items-center justify-between mb-4">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-{{ $program['color'] }}/10 text-{{ $program['color'] }}">
                            {{ $program['target'] }}
                        </span>
                        <div class="text-right text-sm text-muted-foreground">
                            <div>{{ $program['duration'] }}</div>
                            <div>{{ $program['format'] }}</div>
                        </div>
                    </div>
                    
                    <h3 class="text-2xl font-bold text-foreground mb-3 group-hover:text-primary transition-smooth">
                        {{ $program['title'] }}
                    </h3>
                    
                    <p class="text-muted-foreground mb-6 leading-relaxed">
                        {{ $program['description'] }}
                    </p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Training Modules -->
                        <div>
                            <h4 class="font-semibold text-foreground mb-3 flex items-center">
                                <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                </svg>
                                Training Modules
                            </h4>
                            <ul class="space-y-2">
                                @foreach($program['modules'] as $module)
                                    <li class="flex items-start space-x-2">
                                        <svg class="w-4 h-4 text-primary mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"/>
                                        </svg>
                                        <span class="text-sm text-muted-foreground">{{ $module }}</span>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                        
                        <!-- Program Benefits -->
                        <div>
                            <h4 class="font-semibold text-foreground mb-3 flex items-center">
                                <svg class="w-5 h-5 text-success mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                Program Benefits
                            </h4>
                            <ul class="space-y-2">
                                @foreach($program['benefits'] as $benefit)
                                    <li class="flex items-start space-x-2">
                                        <svg class="w-4 h-4 text-success mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        <span class="text-sm text-muted-foreground">{{ $benefit }}</span>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-6 pt-6 border-t border-border">
                        <x-button href="#" size="sm" class="w-full">
                            Enroll Now
                        </x-button>
                    </div>
                </x-card>
            @endforeach
        </div>
    </x-section>

    <!-- Training Statistics -->
    <x-section background="muted" padding="lg">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Training Impact</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Our training programs have made measurable improvements in road safety knowledge and behavior.
            </p>
        </div>
        
        <x-stats :stats="[
            [
                'value' => '5,000+',
                'label' => 'Professionals Trained',
                'description' => 'Across all certification programs',
                'icon' => '<svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"/></svg>'
            ],
            [
                'value' => '95%',
                'label' => 'Completion Rate',
                'description' => 'Average across all programs',
                'icon' => '<svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"/></svg>'
            ],
            [
                'value' => '4.8/5',
                'label' => 'Satisfaction Rating',
                'description' => 'Participant feedback score',
                'icon' => '<svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"/></svg>'
            ],
            [
                'value' => '75%',
                'label' => 'Behavior Improvement',
                'description' => 'Post-training assessment results',
                'icon' => '<svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"/></svg>'
            ]
        ]" />
    </x-section>

    <!-- Success Stories -->
    <x-section padding="lg">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Success Stories</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Real stories from participants who have transformed their approach to road safety through our training.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @php
            $stories = [
                [
                    'name' => 'Marcus Johnson',
                    'role' => 'Fleet Manager',
                    'company' => 'City Transport Co.',
                    'program' => 'Professional Driver Certification',
                    'quote' => 'The training transformed our entire fleet operation. We\'ve seen a 60% reduction in incidents and our drivers are more confident and professional.',
                    'image' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face'
                ],
                [
                    'name' => 'Sarah Martinez',
                    'role' => 'Elementary Teacher',
                    'company' => 'Lincoln Elementary',
                    'program' => 'Educator Training Program',
                    'quote' => 'The program gave me practical tools to teach road safety in engaging ways. My students are now safety ambassadors in their families.',
                    'image' => 'https://images.unsplash.com/photo-1494790108755-2616b9e0e4d4?w=300&h=300&fit=crop&crop=face'
                ],
                [
                    'name' => 'David Chen',
                    'role' => 'Community Leader',
                    'company' => 'Riverside Neighborhood',
                    'program' => 'Community Safety Leadership',
                    'quote' => 'After completing the program, I organized 5 safety events in our community. We\'ve created a culture of safety awareness that didn\'t exist before.',
                    'image' => 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face'
                ]
            ];
            @endphp
            
            @foreach($stories as $story)
                <x-card class="text-center">
                    <div class="w-20 h-20 mx-auto rounded-full overflow-hidden bg-gradient-to-br from-primary/20 to-primary/5 mb-4">
                        <img src="{{ $story['image'] }}" alt="{{ $story['name'] }}" class="w-full h-full object-cover">
                    </div>
                    
                    <blockquote class="text-muted-foreground italic mb-4 leading-relaxed">
                        "{{ $story['quote'] }}"
                    </blockquote>
                    
                    <div class="text-center">
                        <h4 class="font-semibold text-foreground">{{ $story['name'] }}</h4>
                        <p class="text-sm text-primary">{{ $story['role'] }}</p>
                        <p class="text-xs text-muted-foreground">{{ $story['company'] }}</p>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-secondary/10 text-secondary-foreground mt-2">
                            {{ $story['program'] }}
                        </span>
                    </div>
                </x-card>
            @endforeach
        </div>
    </x-section>

    <!-- Registration CTA -->
    <x-section background="primary" padding="lg">
        <div class="text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-primary-foreground mb-4">
                Ready to Get Trained?
            </h2>
            <p class="text-lg text-primary-foreground/90 max-w-2xl mx-auto mb-8">
                Join thousands of professionals who have enhanced their road safety knowledge and skills through our comprehensive training programs.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <x-button href="#" variant="secondary" size="lg">
                    Browse All Programs
                </x-button>
                <x-button href="{{ route('contact.index') }}" variant="outline" size="lg" class="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
                    Custom Training Request
                </x-button>
            </div>
        </div>
    </x-section>
</x-layouts.app>
