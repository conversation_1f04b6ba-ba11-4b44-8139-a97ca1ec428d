<x-layouts.app title="Our Works - Vibrant Road Safety Awareness Foundation" description="Discover our comprehensive initiatives and projects that are making roads safer through education, advocacy, and community engagement.">
    <!-- Hero Section -->
    <x-hero 
        title="Our <span class='text-safety-yellow'>Impactful Works</span>"
        subtitle="What We Do"
        description="Through innovative programs, community partnerships, and evidence-based approaches, we're creating lasting change in road safety culture."
        size="lg"
    />

    <!-- Current Initiatives -->
    <x-section padding="lg">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Current Initiatives</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Our active programs are making measurable differences in communities across the region.
            </p>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            @php
            $initiatives = [
                [
                    'title' => 'Safe Schools Initiative',
                    'status' => 'Active',
                    'duration' => '2023 - Ongoing',
                    'description' => 'Comprehensive road safety education program targeting students, teachers, and parents in educational institutions.',
                    'impact' => [
                        '150+ schools enrolled',
                        '25,000+ students educated',
                        '500+ teachers trained',
                        '40% reduction in school zone incidents'
                    ],
                    'activities' => [
                        'Interactive safety workshops',
                        'Teacher training programs',
                        'Parent awareness sessions',
                        'School zone safety audits'
                    ],
                    'color' => 'safety-green'
                ],
                [
                    'title' => 'Professional Driver Excellence Program',
                    'status' => 'Active',
                    'duration' => '2022 - Ongoing',
                    'description' => 'Advanced training and certification program for commercial drivers and transport professionals.',
                    'impact' => [
                        '2,000+ drivers certified',
                        '50+ transport companies participating',
                        '60% reduction in commercial vehicle accidents',
                        '95% participant satisfaction rate'
                    ],
                    'activities' => [
                        'Defensive driving courses',
                        'Vehicle maintenance training',
                        'Emergency response protocols',
                        'Continuous assessment programs'
                    ],
                    'color' => 'primary'
                ],
                [
                    'title' => 'Community Safety Champions',
                    'status' => 'Active',
                    'duration' => '2021 - Ongoing',
                    'description' => 'Grassroots program that trains community members to become local road safety advocates and educators.',
                    'impact' => [
                        '800+ champions trained',
                        '120+ communities reached',
                        '30,000+ people educated',
                        '50% increase in safety awareness'
                    ],
                    'activities' => [
                        'Champion training workshops',
                        'Community outreach events',
                        'Peer-to-peer education',
                        'Local safety campaigns'
                    ],
                    'color' => 'safety-orange'
                ],
                [
                    'title' => 'Digital Safety Platform',
                    'status' => 'Active',
                    'duration' => '2024 - Ongoing',
                    'description' => 'Online platform providing interactive safety resources, training modules, and community engagement tools.',
                    'impact' => [
                        '10,000+ registered users',
                        '50+ interactive modules',
                        '200+ safety resources',
                        '85% completion rate'
                    ],
                    'activities' => [
                        'E-learning modules',
                        'Virtual reality simulations',
                        'Mobile safety apps',
                        'Online community forums'
                    ],
                    'color' => 'safety-yellow'
                ]
            ];
            @endphp
            
            @foreach($initiatives as $initiative)
                <x-card variant="elevated" hover="true" class="group">
                    <div class="flex items-center justify-between mb-4">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-{{ $initiative['color'] }}/10 text-{{ $initiative['color'] }}">
                            {{ $initiative['status'] }}
                        </span>
                        <span class="text-sm text-muted-foreground">{{ $initiative['duration'] }}</span>
                    </div>
                    
                    <h3 class="text-2xl font-bold text-foreground mb-3 group-hover:text-primary transition-smooth">
                        {{ $initiative['title'] }}
                    </h3>
                    
                    <p class="text-muted-foreground mb-6 leading-relaxed">
                        {{ $initiative['description'] }}
                    </p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Impact Metrics -->
                        <div>
                            <h4 class="font-semibold text-foreground mb-3 flex items-center">
                                <svg class="w-5 h-5 text-success mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                </svg>
                                Impact Achieved
                            </h4>
                            <ul class="space-y-2">
                                @foreach($initiative['impact'] as $impact)
                                    <li class="flex items-start space-x-2">
                                        <svg class="w-4 h-4 text-success mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        <span class="text-sm text-muted-foreground">{{ $impact }}</span>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                        
                        <!-- Key Activities -->
                        <div>
                            <h4 class="font-semibold text-foreground mb-3 flex items-center">
                                <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                </svg>
                                Key Activities
                            </h4>
                            <ul class="space-y-2">
                                @foreach($initiative['activities'] as $activity)
                                    <li class="flex items-start space-x-2">
                                        <svg class="w-4 h-4 text-primary mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                        </svg>
                                        <span class="text-sm text-muted-foreground">{{ $activity }}</span>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </x-card>
            @endforeach
        </div>
    </x-section>

    <!-- Completed Projects -->
    <x-section background="muted" padding="lg">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Completed Projects</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Our successful past projects have laid the foundation for ongoing safety improvements.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @php
            $completedProjects = [
                [
                    'title' => 'Highway Safety Corridor Project',
                    'duration' => '2020 - 2022',
                    'description' => 'Comprehensive safety improvements along high-risk highway segments.',
                    'outcomes' => ['70% accident reduction', '15 corridors improved', '500+ lives saved'],
                    'budget' => '$2.5M'
                ],
                [
                    'title' => 'Urban Intersection Safety Initiative',
                    'duration' => '2019 - 2021',
                    'description' => 'Targeted interventions at accident-prone urban intersections.',
                    'outcomes' => ['45% injury reduction', '25 intersections upgraded', '1000+ pedestrians protected'],
                    'budget' => '$1.8M'
                ],
                [
                    'title' => 'Rural Road Safety Enhancement',
                    'duration' => '2018 - 2020',
                    'description' => 'Safety improvements and education programs for rural communities.',
                    'outcomes' => ['55% fatality reduction', '50 communities served', '200+ volunteers trained'],
                    'budget' => '$1.2M'
                ],
                [
                    'title' => 'Youth Driver Education Campaign',
                    'duration' => '2019 - 2020',
                    'description' => 'Comprehensive education program targeting young and new drivers.',
                    'outcomes' => ['30% teen accident reduction', '5000+ youth educated', '100+ schools participated'],
                    'budget' => '$800K'
                ],
                [
                    'title' => 'Emergency Response Training Program',
                    'duration' => '2020 - 2021',
                    'description' => 'Training program for first responders and emergency personnel.',
                    'outcomes' => ['25% faster response times', '300+ responders trained', '10 departments certified'],
                    'budget' => '$600K'
                ],
                [
                    'title' => 'Public Awareness Media Campaign',
                    'duration' => '2021 - 2022',
                    'description' => 'Multi-media campaign to raise road safety awareness.',
                    'outcomes' => ['80% awareness increase', '2M+ people reached', '15 media partnerships'],
                    'budget' => '$900K'
                ]
            ];
            @endphp
            
            @foreach($completedProjects as $project)
                <x-card class="group">
                    <div class="flex items-center justify-between mb-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success/10 text-success">
                            Completed
                        </span>
                        <span class="text-sm text-muted-foreground">{{ $project['duration'] }}</span>
                    </div>
                    
                    <h3 class="text-lg font-semibold text-foreground mb-2 group-hover:text-primary transition-smooth">
                        {{ $project['title'] }}
                    </h3>
                    
                    <p class="text-sm text-muted-foreground mb-4 leading-relaxed">
                        {{ $project['description'] }}
                    </p>
                    
                    <div class="space-y-3">
                        <div>
                            <h4 class="text-sm font-medium text-foreground mb-2">Key Outcomes:</h4>
                            <ul class="space-y-1">
                                @foreach($project['outcomes'] as $outcome)
                                    <li class="flex items-center space-x-2">
                                        <svg class="w-3 h-3 text-success flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        <span class="text-xs text-muted-foreground">{{ $outcome }}</span>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                        
                        <div class="pt-3 border-t border-border">
                            <div class="flex items-center justify-between">
                                <span class="text-xs text-muted-foreground">Project Budget:</span>
                                <span class="text-sm font-semibold text-primary">{{ $project['budget'] }}</span>
                            </div>
                        </div>
                    </div>
                </x-card>
            @endforeach
        </div>
    </x-section>

    <!-- Call to Action -->
    <x-section padding="lg">
        <div class="text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">
                Partner With Us
            </h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto mb-8">
                Join us in creating safer roads and saving lives. Whether you're an organization, government agency, or individual, there are many ways to get involved.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <x-button href="{{ route('contact.index') }}" size="lg">
                    Start a Partnership
                </x-button>
                <x-button href="{{ route('volunteer.index') }}" variant="outline" size="lg">
                    Volunteer With Us
                </x-button>
            </div>
        </div>
    </x-section>
</x-layouts.app>
