<nav class="sticky top-0 z-40 w-full border-b border-border transition-smooth">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex-shrink-0">
                <a href="{{ route('home') }}" class="flex items-center space-x-2 group">
                    <div class="w-10 h-10 bg-gradient-to-br from-primary to-primary-hover rounded-lg flex items-center justify-center transition-smooth group-hover:scale-105">
                        <svg class="w-6 h-6 text-primary-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                    <div class="hidden sm:block">
                        <span class="text-lg font-bold text-foreground">VRSAF</span>
                        <p class="text-xs text-muted-foreground -mt-1">Road Safety Foundation</p>
                    </div>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:block">
                <div class="ml-10 flex items-baseline space-x-8">
                    <a href="{{ route('home') }}" class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}">
                        Home
                    </a>
                    
                    <!-- Who We Are Dropdown -->
                    <div class="relative group">
                        <button class="nav-link flex items-center space-x-1 {{ request()->routeIs('about.*') || request()->routeIs('members') ? 'active' : '' }}">
                            <span>Who We Are</span>
                            <svg class="w-4 h-4 transition-transform group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="absolute left-0 mt-2 w-48 bg-card border border-border rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                            <div class="py-1">
                                <a href="{{ route('about.index') }}" class="dropdown-link">About Us</a>
                                <a href="{{ route('members') }}" class="dropdown-link">Our Members</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- What We Do Dropdown -->
                    <div class="relative group">
                        <button class="nav-link flex items-center space-x-1 {{ request()->routeIs('works.*') || request()->routeIs('training.*') || request()->routeIs('education.*') ? 'active' : '' }}">
                            <span>What We Do</span>
                            <svg class="w-4 h-4 transition-transform group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="absolute left-0 mt-2 w-56 bg-card border border-border rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                            <div class="py-1">
                                <a href="{{ route('works.index') }}" class="dropdown-link">Our Works</a>
                                <a href="{{ route('training.index') }}" class="dropdown-link">Capacity Building & Training</a>
                                <a href="{{ route('education.index') }}" class="dropdown-link">Road Safety Education</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Media Dropdown -->
                    <div class="relative group">
                        <button class="nav-link flex items-center space-x-1 {{ request()->routeIs('media.*') || request()->routeIs('gallery.*') ? 'active' : '' }}">
                            <span>Media</span>
                            <svg class="w-4 h-4 transition-transform group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="absolute left-0 mt-2 w-48 bg-card border border-border rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                            <div class="py-1">
                                <a href="{{ route('media.coverage') }}" class="dropdown-link">Media Coverage</a>
                                <a href="{{ route('gallery.index') }}" class="dropdown-link">Gallery</a>
                            </div>
                        </div>
                    </div>
                    
                    <a href="{{ route('blog.index') }}" class="nav-link {{ request()->routeIs('blog.*') ? 'active' : '' }}">
                        Blog
                    </a>
                    
                    <!-- Get Involved Dropdown -->
                    <div class="relative group">
                        <button class="nav-link flex items-center space-x-1 {{ request()->routeIs('volunteer.*') || request()->routeIs('contact.*') ? 'active' : '' }}">
                            <span>Get Involved</span>
                            <svg class="w-4 h-4 transition-transform group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="absolute left-0 mt-2 w-48 bg-card border border-border rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                            <div class="py-1">
                                <a href="{{ route('volunteer.index') }}" class="dropdown-link">Become a Volunteer</a>
                                <a href="{{ route('contact.index') }}" class="dropdown-link">Contact Us</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile menu button -->
            <div class="md:hidden">
                <button id="mobile-menu-button" type="button" class="inline-flex items-center justify-center p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-muted transition-smooth">
                    <span class="sr-only">Open main menu</span>
                    <svg id="mobile-menu-icon" class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                    <svg id="mobile-menu-close-icon" class="hidden h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile menu -->
    <div id="mobile-menu" class="hidden md:hidden bg-card border-t border-border">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 max-h-screen overflow-y-auto">
            <a href="{{ route('home') }}" class="mobile-nav-link {{ request()->routeIs('home') ? 'active' : '' }}">Home</a>
            
            <!-- Who We Are Mobile -->
            <div class="space-y-1">
                <div class="mobile-nav-section">Who We Are</div>
                <a href="{{ route('about.index') }}" class="mobile-nav-link ml-4 {{ request()->routeIs('about.*') ? 'active' : '' }}">About Us</a>
                <a href="{{ route('members') }}" class="mobile-nav-link ml-4 {{ request()->routeIs('members') ? 'active' : '' }}">Our Members</a>
            </div>
            
            <!-- What We Do Mobile -->
            <div class="space-y-1">
                <div class="mobile-nav-section">What We Do</div>
                <a href="{{ route('works.index') }}" class="mobile-nav-link ml-4 {{ request()->routeIs('works.*') ? 'active' : '' }}">Our Works</a>
                <a href="{{ route('training.index') }}" class="mobile-nav-link ml-4 {{ request()->routeIs('training.*') ? 'active' : '' }}">Capacity Building & Training</a>
                <a href="{{ route('education.index') }}" class="mobile-nav-link ml-4 {{ request()->routeIs('education.*') ? 'active' : '' }}">Road Safety Education</a>
            </div>
            
            <!-- Media Mobile -->
            <div class="space-y-1">
                <div class="mobile-nav-section">Media</div>
                <a href="{{ route('media.coverage') }}" class="mobile-nav-link ml-4 {{ request()->routeIs('media.*') ? 'active' : '' }}">Media Coverage</a>
                <a href="{{ route('gallery.index') }}" class="mobile-nav-link ml-4 {{ request()->routeIs('gallery.*') ? 'active' : '' }}">Gallery</a>
            </div>
            
            <a href="{{ route('blog.index') }}" class="mobile-nav-link {{ request()->routeIs('blog.*') ? 'active' : '' }}">Blog</a>
            
            <!-- Get Involved Mobile -->
            <div class="space-y-1">
                <div class="mobile-nav-section">Get Involved</div>
                <a href="{{ route('volunteer.index') }}" class="mobile-nav-link ml-4 {{ request()->routeIs('volunteer.*') ? 'active' : '' }}">Become a Volunteer</a>
                <a href="{{ route('contact.index') }}" class="mobile-nav-link ml-4 {{ request()->routeIs('contact.*') ? 'active' : '' }}">Contact Us</a>
            </div>
        </div>
    </div>
</nav>

<style>
.nav-link {
    @apply text-muted-foreground hover:text-foreground px-3 py-2 rounded-md text-sm font-medium transition-smooth;
}

.nav-link.active {
    @apply text-primary bg-primary/10;
}

.dropdown-link {
    @apply block px-4 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-muted transition-smooth;
}

.mobile-nav-link {
    @apply block px-3 py-2 rounded-md text-base font-medium text-muted-foreground hover:text-foreground hover:bg-muted transition-smooth;
}

.mobile-nav-link.active {
    @apply text-primary bg-primary/10;
}

.mobile-nav-section {
    @apply px-3 py-2 text-sm font-semibold text-foreground uppercase tracking-wider;
}
</style>
