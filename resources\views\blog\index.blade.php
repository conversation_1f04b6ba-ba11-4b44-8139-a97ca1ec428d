<x-layouts.app title="Blog - Vibrant Road Safety Awareness Foundation" description="Stay informed with the latest insights, tips, and stories about road safety education, community initiatives, and traffic safety research.">
    <!-- Hero Section -->
    <x-hero 
        title="Road Safety <span class='text-safety-yellow'>Insights</span>"
        subtitle="Our Blog"
        description="Stay informed with the latest insights, research findings, safety tips, and success stories from our road safety initiatives and community programs."
        size="lg"
    />

    <!-- Featured Post -->
    <x-section padding="lg">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Featured Article</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Our most recent and impactful content highlighting important road safety topics.
            </p>
        </div>
        
        @php
        $featuredPost = [
            'title' => 'The Psychology of Road Safety: Understanding Driver Behavior',
            'excerpt' => 'Exploring the psychological factors that influence driving behavior and how understanding these can lead to more effective safety interventions and education programs.',
            'content' => 'Road safety is not just about rules and regulations; it\'s deeply rooted in human psychology and behavior. Understanding why people make certain decisions behind the wheel is crucial for developing effective safety programs...',
            'author' => 'Dr. <PERSON>',
            'authorRole' => 'Executive Director',
            'authorImage' => 'https://images.unsplash.com/photo-1494790108755-2616b9e0e4d4?w=100&h=100&fit=crop&crop=face',
            'date' => 'March 18, 2024',
            'readTime' => '8 min read',
            'category' => 'Research',
            'tags' => ['Psychology', 'Driver Behavior', 'Safety Education', 'Research'],
            'image' => 'https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=800&h=400&fit=crop',
            'slug' => 'psychology-of-road-safety-understanding-driver-behavior'
        ];
        @endphp
        
        <div class="max-w-4xl mx-auto">
            <x-card variant="elevated" hover="true" class="overflow-hidden group">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="aspect-video lg:aspect-square bg-gradient-to-br from-primary/20 to-primary/5 rounded-lg overflow-hidden">
                        <img src="{{ $featuredPost['image'] }}" alt="{{ $featuredPost['title'] }}" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                    </div>
                    <div class="flex flex-col justify-center">
                        <div class="flex items-center space-x-3 mb-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary/10 text-primary">
                                {{ $featuredPost['category'] }}
                            </span>
                            <span class="text-sm text-muted-foreground">{{ $featuredPost['readTime'] }}</span>
                        </div>
                        
                        <h3 class="text-2xl md:text-3xl font-bold text-foreground mb-4 group-hover:text-primary transition-smooth">
                            {{ $featuredPost['title'] }}
                        </h3>
                        
                        <p class="text-muted-foreground mb-6 leading-relaxed">
                            {{ $featuredPost['excerpt'] }}
                        </p>
                        
                        <div class="flex items-center space-x-4 mb-6">
                            <div class="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-secondary/20 to-secondary/5">
                                <img src="{{ $featuredPost['authorImage'] }}" alt="{{ $featuredPost['author'] }}" class="w-full h-full object-cover">
                            </div>
                            <div>
                                <h4 class="font-semibold text-foreground">{{ $featuredPost['author'] }}</h4>
                                <p class="text-sm text-muted-foreground">{{ $featuredPost['authorRole'] }} • {{ $featuredPost['date'] }}</p>
                            </div>
                        </div>
                        
                        <div class="flex flex-wrap gap-2 mb-6">
                            @foreach($featuredPost['tags'] as $tag)
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-secondary/10 text-secondary-foreground">
                                    #{{ $tag }}
                                </span>
                            @endforeach
                        </div>
                        
                        <x-button href="{{ route('blog.show', $featuredPost['slug']) }}" size="lg">
                            Read Full Article
                            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </x-button>
                    </div>
                </div>
            </x-card>
        </div>
    </x-section>

    <!-- Blog Categories Filter -->
    <x-section background="muted" padding="sm">
        <div class="max-w-4xl mx-auto">
            <div class="flex flex-wrap justify-center gap-2 p-4 bg-card rounded-lg border border-border">
                <button class="category-filter active" data-category="all">All Posts</button>
                <button class="category-filter" data-category="safety-tips">Safety Tips</button>
                <button class="category-filter" data-category="research">Research</button>
                <button class="category-filter" data-category="success-stories">Success Stories</button>
                <button class="category-filter" data-category="community">Community</button>
                <button class="category-filter" data-category="training">Training</button>
                <button class="category-filter" data-category="policy">Policy</button>
            </div>
        </div>
    </x-section>

    <!-- Recent Blog Posts -->
    <x-section padding="lg">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Recent Articles</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Explore our latest insights, tips, and stories about road safety and community initiatives.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="blog-grid">
            @php
            $blogPosts = [
                [
                    'title' => '10 Essential Road Safety Tips Every Driver Should Know',
                    'excerpt' => 'Discover the fundamental safety practices that can prevent accidents and save lives on our roads.',
                    'author' => 'Michael Chen',
                    'authorRole' => 'Program Director',
                    'authorImage' => 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
                    'date' => 'March 15, 2024',
                    'readTime' => '5 min read',
                    'category' => 'safety-tips',
                    'categoryLabel' => 'Safety Tips',
                    'image' => 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=250&fit=crop',
                    'slug' => '10-essential-road-safety-tips-every-driver-should-know'
                ],
                [
                    'title' => 'The Impact of Mobile Phones on Road Safety',
                    'excerpt' => 'Understanding the dangers of distracted driving and how to stay focused behind the wheel.',
                    'author' => 'Dr. Priya Patel',
                    'authorRole' => 'Research Director',
                    'authorImage' => 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=100&h=100&fit=crop&crop=face',
                    'date' => 'March 12, 2024',
                    'readTime' => '7 min read',
                    'category' => 'research',
                    'categoryLabel' => 'Research',
                    'image' => 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=250&fit=crop',
                    'slug' => 'impact-of-mobile-phones-on-road-safety'
                ],
                [
                    'title' => 'Community Success Story: Reducing Accidents by 40%',
                    'excerpt' => 'How one community transformed their road safety culture through our comprehensive program.',
                    'author' => 'Lisa Rodriguez',
                    'authorRole' => 'Training Coordinator',
                    'authorImage' => 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
                    'date' => 'March 10, 2024',
                    'readTime' => '4 min read',
                    'category' => 'success-stories',
                    'categoryLabel' => 'Success Stories',
                    'image' => 'https://images.unsplash.com/photo-1559027615-cd4628902d4a?w=400&h=250&fit=crop',
                    'slug' => 'community-success-story-reducing-accidents-by-40-percent'
                ],
                [
                    'title' => 'Building Effective School Safety Programs',
                    'excerpt' => 'Best practices for implementing comprehensive road safety education in schools.',
                    'author' => 'David Thompson',
                    'authorRole' => 'Community Engagement Manager',
                    'authorImage' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
                    'date' => 'March 8, 2024',
                    'readTime' => '6 min read',
                    'category' => 'training',
                    'categoryLabel' => 'Training',
                    'image' => 'https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=400&h=250&fit=crop',
                    'slug' => 'building-effective-school-safety-programs'
                ],
                [
                    'title' => 'The Role of Technology in Modern Road Safety',
                    'excerpt' => 'Exploring how emerging technologies are revolutionizing road safety education and enforcement.',
                    'author' => 'James Wilson',
                    'authorRole' => 'Operations Manager',
                    'authorImage' => 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face',
                    'date' => 'March 5, 2024',
                    'readTime' => '8 min read',
                    'category' => 'research',
                    'categoryLabel' => 'Research',
                    'image' => 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=250&fit=crop',
                    'slug' => 'role-of-technology-in-modern-road-safety'
                ],
                [
                    'title' => 'Policy Changes That Could Save Lives',
                    'excerpt' => 'Analyzing recent policy proposals and their potential impact on road safety outcomes.',
                    'author' => 'Dr. Sarah Johnson',
                    'authorRole' => 'Executive Director',
                    'authorImage' => 'https://images.unsplash.com/photo-1494790108755-2616b9e0e4d4?w=100&h=100&fit=crop&crop=face',
                    'date' => 'March 3, 2024',
                    'readTime' => '9 min read',
                    'category' => 'policy',
                    'categoryLabel' => 'Policy',
                    'image' => 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=400&h=250&fit=crop',
                    'slug' => 'policy-changes-that-could-save-lives'
                ]
            ];
            @endphp
            
            @foreach($blogPosts as $post)
                <article class="blog-post group" data-category="{{ $post['category'] }}">
                    <x-card hover="true" class="h-full flex flex-col">
                        <div class="aspect-video bg-gradient-to-br from-primary/20 to-primary/5 rounded-lg overflow-hidden mb-4">
                            <img src="{{ $post['image'] }}" alt="{{ $post['title'] }}" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                        </div>
                        
                        <div class="flex-1 flex flex-col">
                            <div class="flex items-center justify-between mb-3">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
                                    {{ $post['categoryLabel'] }}
                                </span>
                                <span class="text-sm text-muted-foreground">{{ $post['readTime'] }}</span>
                            </div>
                            
                            <h3 class="text-xl font-bold text-foreground mb-3 group-hover:text-primary transition-smooth line-clamp-2">
                                {{ $post['title'] }}
                            </h3>
                            
                            <p class="text-muted-foreground mb-4 leading-relaxed line-clamp-3 flex-1">
                                {{ $post['excerpt'] }}
                            </p>
                            
                            <div class="flex items-center space-x-3 mb-4">
                                <div class="w-8 h-8 rounded-full overflow-hidden bg-gradient-to-br from-secondary/20 to-secondary/5">
                                    <img src="{{ $post['authorImage'] }}" alt="{{ $post['author'] }}" class="w-full h-full object-cover">
                                </div>
                                <div>
                                    <h4 class="text-sm font-semibold text-foreground">{{ $post['author'] }}</h4>
                                    <p class="text-xs text-muted-foreground">{{ $post['date'] }}</p>
                                </div>
                            </div>
                            
                            <x-button href="{{ route('blog.show', $post['slug']) }}" variant="ghost" size="sm" class="self-start">
                                Read More
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                </svg>
                            </x-button>
                        </div>
                    </x-card>
                </article>
            @endforeach
        </div>
        
        <!-- Load More Button -->
        <div class="text-center mt-12">
            <x-button id="load-more-posts" variant="outline" size="lg">
                Load More Articles
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
                </svg>
            </x-button>
        </div>
    </x-section>

    <!-- Newsletter Signup -->
    <x-section background="primary" padding="lg">
        <div class="text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-primary-foreground mb-4">
                Stay Updated
            </h2>
            <p class="text-lg text-primary-foreground/90 max-w-2xl mx-auto mb-8">
                Subscribe to our newsletter to receive the latest road safety insights, tips, and updates directly in your inbox.
            </p>
            <div class="max-w-md mx-auto">
                <form class="flex flex-col sm:flex-row gap-3">
                    <input 
                        type="email" 
                        placeholder="Enter your email address" 
                        class="flex-1 px-4 py-3 rounded-md border border-primary-foreground/20 bg-primary-foreground/10 text-primary-foreground placeholder-primary-foreground/70 focus:outline-none focus:ring-2 focus:ring-primary-foreground/50"
                        required
                    >
                    <x-button type="submit" variant="secondary" size="lg">
                        Subscribe
                    </x-button>
                </form>
            </div>
        </div>
    </x-section>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const categoryFilters = document.querySelectorAll('.category-filter');
            const blogPosts = document.querySelectorAll('.blog-post');
            const loadMoreBtn = document.getElementById('load-more-posts');
            
            // Category filter functionality
            categoryFilters.forEach(filter => {
                filter.addEventListener('click', function() {
                    const category = this.dataset.category;
                    
                    // Update active filter
                    categoryFilters.forEach(f => f.classList.remove('active'));
                    this.classList.add('active');
                    
                    // Filter posts
                    blogPosts.forEach(post => {
                        if (category === 'all' || post.dataset.category === category) {
                            post.style.display = 'block';
                            post.style.animation = 'fadeIn 0.5s ease-in-out';
                        } else {
                            post.style.display = 'none';
                        }
                    });
                });
            });
            
            // Load more functionality
            loadMoreBtn.addEventListener('click', function() {
                this.textContent = 'Loading...';
                // Simulate loading
                setTimeout(() => {
                    this.textContent = 'Load More Articles';
                    // In a real implementation, this would load more posts via AJAX
                }, 1000);
            });
        });
    </script>
    
    <style>
        .category-filter {
            @apply px-4 py-2 text-sm font-medium text-muted-foreground bg-background border border-border rounded-md hover:bg-muted hover:text-foreground transition-smooth;
        }
        
        .category-filter.active {
            @apply bg-primary text-primary-foreground border-primary;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
    @endpush
</x-layouts.app>
