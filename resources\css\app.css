@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

/* Root Theme Variables - Dynamic Color System */
:root {
    /* Primary Brand Colors */
    --primary: 220 38% 57%;        /* Blue primary */
    --primary-foreground: 210 40% 98%;
    --primary-hover: 220 38% 47%;

    /* Secondary Colors */
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --secondary-hover: 210 40% 90%;

    /* Accent Colors */
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --accent-hover: 210 40% 90%;

    /* Background Colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Muted Colors */
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* Border and Input */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 220 38% 57%;

    /* Destructive */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* Success */
    --success: 142.1 76.2% 36.3%;
    --success-foreground: 355.7 100% 97.3%;

    /* Warning */
    --warning: 32.5 94.6% 43.7%;
    --warning-foreground: 210 40% 98%;

    /* Road Safety Theme Colors */
    --safety-red: 0 84% 60%;
    --safety-yellow: 45 93% 47%;
    --safety-green: 142 76% 36%;
    --safety-orange: 25 95% 53%;

    /* Radius */
    --radius: 0.5rem;
}

/* Dark theme support (optional for future) */
.dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 220 38% 57%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 220 38% 57%;
}

@theme {
    --font-sans: 'Inter', 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';

    /* Custom color palette using CSS variables */
    --color-primary: hsl(var(--primary));
    --color-primary-foreground: hsl(var(--primary-foreground));
    --color-primary-hover: hsl(var(--primary-hover));

    --color-secondary: hsl(var(--secondary));
    --color-secondary-foreground: hsl(var(--secondary-foreground));
    --color-secondary-hover: hsl(var(--secondary-hover));

    --color-accent: hsl(var(--accent));
    --color-accent-foreground: hsl(var(--accent-foreground));
    --color-accent-hover: hsl(var(--accent-hover));

    --color-background: hsl(var(--background));
    --color-foreground: hsl(var(--foreground));
    --color-card: hsl(var(--card));
    --color-card-foreground: hsl(var(--card-foreground));

    --color-muted: hsl(var(--muted));
    --color-muted-foreground: hsl(var(--muted-foreground));

    --color-border: hsl(var(--border));
    --color-input: hsl(var(--input));
    --color-ring: hsl(var(--ring));

    --color-destructive: hsl(var(--destructive));
    --color-destructive-foreground: hsl(var(--destructive-foreground));

    --color-success: hsl(var(--success));
    --color-success-foreground: hsl(var(--success-foreground));

    --color-warning: hsl(var(--warning));
    --color-warning-foreground: hsl(var(--warning-foreground));

    /* Road Safety Theme Colors */
    --color-safety-red: hsl(var(--safety-red));
    --color-safety-yellow: hsl(var(--safety-yellow));
    --color-safety-green: hsl(var(--safety-green));
    --color-safety-orange: hsl(var(--safety-orange));
}

/* Custom utility classes for smooth animations */
.transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-bounce {
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
    background: hsl(var(--primary));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary-hover));
}

/* Performance optimizations */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Focus styles for accessibility */
*:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
}

/* Theme switcher utility classes */
.theme-light {
    /* Light theme is default */
}

.theme-dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 220 38% 57%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 220 38% 57%;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }

    a,
    a:visited {
        text-decoration: underline;
    }

    a[href]:after {
        content: " (" attr(href) ")";
    }

    abbr[title]:after {
        content: " (" attr(title) ")";
    }

    .page-break {
        page-break-before: always;
    }
}
