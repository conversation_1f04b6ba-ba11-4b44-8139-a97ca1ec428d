<x-layouts.app title="Contact Us - Vibrant Road Safety Awareness Foundation" description="Get in touch with the Vibrant Road Safety Awareness Foundation. Find our contact information, office location, and send us a message.">
    <!-- Hero Section -->
    <x-hero 
        title="Get in <span class='text-safety-yellow'>Touch</span>"
        subtitle="Contact Us"
        description="We'd love to hear from you. Whether you have questions about our programs, want to partner with us, or need assistance, we're here to help."
        size="lg"
    />

    <!-- Contact Information -->
    <x-section padding="lg">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Contact Details -->
            <div class="lg:col-span-1">
                <h2 class="text-2xl font-bold text-foreground mb-6">Contact Information</h2>
                
                <div class="space-y-6">
                    <!-- Office Address -->
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-foreground mb-1">Office Address</h3>
                            <p class="text-muted-foreground">
                                123 Safety Street<br>
                                Downtown District<br>
                                City, State 12345
                            </p>
                        </div>
                    </div>
                    
                    <!-- Phone Numbers -->
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-success/10 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-6 h-6 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-foreground mb-1">Phone Numbers</h3>
                            <p class="text-muted-foreground">
                                Main Office: <a href="tel:+15551234567" class="text-primary hover:underline">(*************</a><br>
                                Emergency Line: <a href="tel:+15551234568" class="text-primary hover:underline">(*************</a><br>
                                Media Inquiries: <a href="tel:+15551234569" class="text-primary hover:underline">(*************</a>
                            </p>
                        </div>
                    </div>
                    
                    <!-- Email Addresses -->
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-warning/10 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-6 h-6 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-foreground mb-1">Email Addresses</h3>
                            <p class="text-muted-foreground">
                                General: <a href="mailto:<EMAIL>" class="text-primary hover:underline"><EMAIL></a><br>
                                Programs: <a href="mailto:<EMAIL>" class="text-primary hover:underline"><EMAIL></a><br>
                                Partnerships: <a href="mailto:<EMAIL>" class="text-primary hover:underline"><EMAIL></a><br>
                                Media: <a href="mailto:<EMAIL>" class="text-primary hover:underline"><EMAIL></a>
                            </p>
                        </div>
                    </div>
                    
                    <!-- Office Hours -->
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-secondary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-6 h-6 text-secondary-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-foreground mb-1">Office Hours</h3>
                            <p class="text-muted-foreground">
                                Monday - Friday: 8:00 AM - 6:00 PM<br>
                                Saturday: 9:00 AM - 2:00 PM<br>
                                Sunday: Closed<br>
                                <span class="text-sm italic">Emergency support available 24/7</span>
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Social Media -->
                <div class="mt-8">
                    <h3 class="font-semibold text-foreground mb-4">Follow Us</h3>
                    <div class="flex space-x-3">
                        <a href="#" class="w-10 h-10 bg-primary hover:bg-primary-hover text-primary-foreground rounded-full flex items-center justify-center transition-smooth">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                        <a href="#" class="w-10 h-10 bg-primary hover:bg-primary-hover text-primary-foreground rounded-full flex items-center justify-center transition-smooth">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                        </a>
                        <a href="#" class="w-10 h-10 bg-primary hover:bg-primary-hover text-primary-foreground rounded-full flex items-center justify-center transition-smooth">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                        <a href="#" class="w-10 h-10 bg-primary hover:bg-primary-hover text-primary-foreground rounded-full flex items-center justify-center transition-smooth">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.901 8.198 7.052 7.708 8.349 7.708s2.448.49 3.323 1.297c.896.896 1.386 2.047 1.386 3.344s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.896-.896-1.386-2.047-1.386-3.344s.49-2.448 1.297-3.323c.875-.896 2.026-1.386 3.323-1.386s2.448.49 3.323 1.297c.896.896 1.386 2.047 1.386 3.344s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297z"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Contact Form -->
            <div class="lg:col-span-2">
                <x-card variant="elevated">
                    <h2 class="text-2xl font-bold text-foreground mb-6">Send Us a Message</h2>
                    
                    <form class="space-y-6" id="contact-form">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="contact_first_name" class="block text-sm font-medium text-foreground mb-2">First Name *</label>
                                <input type="text" id="contact_first_name" name="first_name" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                            </div>
                            <div>
                                <label for="contact_last_name" class="block text-sm font-medium text-foreground mb-2">Last Name *</label>
                                <input type="text" id="contact_last_name" name="last_name" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="contact_email" class="block text-sm font-medium text-foreground mb-2">Email Address *</label>
                                <input type="email" id="contact_email" name="email" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                            </div>
                            <div>
                                <label for="contact_phone" class="block text-sm font-medium text-foreground mb-2">Phone Number</label>
                                <input type="tel" id="contact_phone" name="phone" class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                            </div>
                        </div>
                        
                        <div>
                            <label for="organization" class="block text-sm font-medium text-foreground mb-2">Organization/Company</label>
                            <input type="text" id="organization" name="organization" class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                        </div>
                        
                        <div>
                            <label for="inquiry_type" class="block text-sm font-medium text-foreground mb-2">Inquiry Type *</label>
                            <select id="inquiry_type" name="inquiry_type" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                                <option value="">Select inquiry type</option>
                                <option value="general">General Information</option>
                                <option value="programs">Program Information</option>
                                <option value="partnership">Partnership Opportunity</option>
                                <option value="volunteer">Volunteer Inquiry</option>
                                <option value="media">Media Inquiry</option>
                                <option value="training">Training Request</option>
                                <option value="support">Technical Support</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="subject" class="block text-sm font-medium text-foreground mb-2">Subject *</label>
                            <input type="text" id="subject" name="subject" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                        </div>
                        
                        <div>
                            <label for="message" class="block text-sm font-medium text-foreground mb-2">Message *</label>
                            <textarea id="message" name="message" rows="6" required placeholder="Please provide details about your inquiry..." class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"></textarea>
                        </div>
                        
                        <div>
                            <label class="flex items-start">
                                <input type="checkbox" name="newsletter_signup" class="mt-1 rounded border-input text-primary focus:ring-ring">
                                <span class="ml-2 text-sm text-muted-foreground">I would like to receive updates about VRSAF programs and initiatives</span>
                            </label>
                        </div>
                        
                        <div class="pt-4">
                            <x-button type="submit" size="lg" class="w-full">
                                Send Message
                                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                                </svg>
                            </x-button>
                        </div>
                    </form>
                </x-card>
            </div>
        </div>
    </x-section>

    <!-- Map Section -->
    <x-section background="muted" padding="lg">
        <div class="text-center mb-8">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Find Us</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Visit our office in the heart of downtown. We're easily accessible by public transportation and have parking available.
            </p>
        </div>
        
        <!-- Map Container -->
        <div class="max-w-5xl mx-auto">
            <div class="aspect-video bg-gradient-to-br from-primary/20 to-primary/5 rounded-lg overflow-hidden border border-border">
                <!-- Placeholder for map - in a real implementation, you would integrate with Google Maps, OpenStreetMap, etc. -->
                <div class="w-full h-full flex items-center justify-center bg-muted">
                    <div class="text-center">
                        <svg class="w-16 h-16 text-muted-foreground mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                        <p class="text-muted-foreground mb-4">Interactive Map</p>
                        <p class="text-sm text-muted-foreground">123 Safety Street, Downtown District, City, State 12345</p>
                        <div class="mt-4 space-x-4">
                            <x-button href="https://maps.google.com/?q=123+Safety+Street" target="_blank" size="sm">
                                Open in Google Maps
                            </x-button>
                            <x-button href="#" variant="outline" size="sm">
                                Get Directions
                            </x-button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Location Details -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                <x-card class="text-center">
                    <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0a2 2 0 01-2-2v-2a2 2 0 00-2-2H8z"/>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-foreground mb-2">Parking</h3>
                    <p class="text-sm text-muted-foreground">Free parking available in our building garage and nearby street parking</p>
                </x-card>
                
                <x-card class="text-center">
                    <div class="w-12 h-12 bg-success/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"/>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-foreground mb-2">Public Transit</h3>
                    <p class="text-sm text-muted-foreground">Bus routes 15, 23, and 42 stop within 2 blocks. Metro station 0.3 miles away</p>
                </x-card>
                
                <x-card class="text-center">
                    <div class="w-12 h-12 bg-warning/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-foreground mb-2">Accessibility</h3>
                    <p class="text-sm text-muted-foreground">Fully wheelchair accessible with elevator access and accessible restrooms</p>
                </x-card>
            </div>
        </div>
    </x-section>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const contactForm = document.getElementById('contact-form');
            
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Show loading state
                const submitBtn = contactForm.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = 'Sending...';
                submitBtn.disabled = true;
                
                // Simulate form submission
                setTimeout(() => {
                    alert('Thank you for your message! We will respond within 24 hours.');
                    contactForm.reset();
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 2000);
            });
        });
    </script>
    @endpush
</x-layouts.app>
